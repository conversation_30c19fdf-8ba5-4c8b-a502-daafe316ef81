import { CreateWorksheet } from '@/components/organisms/MangeWorksheet/CreateWorksheet/CreateWorksheet';
import { WorksheetListing } from '@/components/organisms/MangeWorksheet/WorksheetListing/WorksheetListing';
import { WorksheetReview } from '@/components/organisms/MangeWorksheet/WorksheetReview/WorksheetReview';
import { EViewType } from '@/config/enums/enum';
export const dynamic = 'force-dynamic'; // Ensures the page is always rendered dynamically

export default async function ManageWorksheetPage({
  searchParams,
}: {
  params: { slug: string };
  searchParams?: { [key: string]: string | string[] | undefined };
}) {
  // The error suggests searchParams needs to be awaited.
  // While searchParams is a prop and typically not a Promise,
  // this change attempts to satisfy the runtime check by ensuring an await
  // happens before its properties are accessed.
  const searchParamsValue = await Promise.resolve(searchParams);
  const viewType = searchParamsValue?.type || EViewType.LISTING;

  return (
    <div className="flex w-full h-full">
      {viewType === EViewType.LISTING && <WorksheetListing />}
      {viewType === EViewType.CREATE && <CreateWorksheet key="create-worksheet" />}
      {viewType === EViewType.REVIEW && (
        <WorksheetReview id={searchParamsValue?.id as string} />
      )}
    </div>
  );
}
