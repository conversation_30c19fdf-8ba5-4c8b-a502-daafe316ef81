import { BsDownload } from 'react-icons/bs';

import { Button, Tag } from '@/components/atoms';

import { TListingTableWithExportProps } from './CustomTable.types';
import TableCommonClientSort from './CustomTableClientSort';
import { ActionLink } from '@/components/atoms/shared';
import { APP_ROUTE } from '@/constants/route.constant';
import { EOnGoingProjectDetailTab } from '@/@types/enum/tab.enum';

const ListTableWithExport = <T extends { id?: string; title?: string }>({
  tableList,
  handleClickExport,
  isLoading,
  columns,
  exportTableId,
  tableConfigName,
  ...rest
}: TListingTableWithExportProps<T>) => {
  const tableData: T[] = tableList?.length ? tableList : ([{}] as T[]);
  return (
    <>
      {tableData?.map((table, index) => {
        const tableId = table?.id;
        const projectDetailLink = `${APP_ROUTE.PROJECTS.SUB_ROUTES.ON_GOING}/${tableId}?tab=${EOnGoingProjectDetailTab.PROJECT_DETAIL}`;
        return (
          <div key={tableId || index} className="flex-col mb-6 gap-4">
            {tableId && (
              <div className="flex gap-3">
                <Tag className="w-fit rounded-2 border-gray-700 text-14-400">
                  <ActionLink href={projectDetailLink}>
                    {table?.title}
                  </ActionLink>
                </Tag>
                <Button
                  size="sm"
                  className="btn-export border-gray-700 text-14-400"
                  icon={<BsDownload size={16} />}
                  loading={exportTableId === tableId}
                  onClick={() => handleClickExport([table])}
                >
                  Export
                </Button>
              </div>
            )}
            <TableCommonClientSort<T>
              {...rest}
              isLoading={isLoading}
              tableData={tableId ? [table] : []}
              columns={columns}
              isShowPagination={false}
              tableConfigName={tableConfigName}
            />
          </div>
        );
      })}
    </>
  );
};

export default ListTableWithExport;
