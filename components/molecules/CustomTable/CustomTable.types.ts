import { ColumnDef, Row } from '@tanstack/react-table';

export type TTableCommonProps<T> = {
  isLoading?: boolean;
  wrapperClass?: string;
  theadClass?: string;
  tbodyClass?: string;
  tableHeight?: `h-${string}`;
  columns: ColumnDef<T, string>[];
  tableData: T[];
  handleClickTableRow?: (row: Row<T & { theadColClass?: string }>) => void;
};

export type TTableRowData<T> = Row<T & { theadColClass?: string }>;
export type TTableCommonClientSort<T> = TTableCommonProps<T> & {
  isShowPagination: boolean;
};

export type TListingTableWithExportProps<T> = Omit<
  TTableCommonClientSort<T>,
  'tableData'
> & {
  tableList: T[];
  handleClickExport: (table: T[]) => void;
  exportTableId?: string | null;
};
