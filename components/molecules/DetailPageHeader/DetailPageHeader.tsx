'use client';

import React from 'react';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';

export interface DetailPageHeaderProps {
  title: string;
  backLink: string;
  backText?: string;
  className?: string;
}

export const DetailPageHeader: React.FC<DetailPageHeaderProps> = ({
  title,
  backLink,
  backText = 'Back',
  className = '',
}) => {
  return (
    <div className={`flex items-center mb-6 ${className}`}>
      <Link 
        href={backLink} 
        className="btn btn-ghost mr-4"
      >
        <ArrowLeft size={18} />
        {backText}
      </Link>
      <h1 className="text-2xl font-bold">{title}</h1>
    </div>
  );
};