'use client';

import React,
{ useState } from 'react';
import { use<PERSON><PERSON>, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/atoms/Button/Button';
import { Input } from '@/components/atoms/Input/Input';
import { Form } from '@/components/atoms/Form/Form';
import { Label } from '@/components/atoms/Label/Label';
import { EUserRole } from '@/config/enums/user';
import { handleCreateUserAction } from '@/actions/user.action'; // Import the server action

interface ICreateUserFormInput {
  name: string;
  email: string;
  password: string;
  role: EUserRole;
  schoolId?: string;
}

const createUserSchema = z.object({
  name: z.string().min(1, { message: 'Name is required' }),
  email: z.string().email({ message: 'Invalid email address' }),
  password: z.string().min(6, { message: 'Password must be at least 6 characters' }),
  role: z.nativeEnum(EUserRole),
  schoolId: z.string().optional(),
}).refine(data => {
  if ([EUserRole.TEACHER, EUserRole.STUDENT, EUserRole.SCHOOL_MANAGER].includes(data.role) && !data.schoolId) {
    return false;
  }
  return true;
}, {
  message: 'School ID is required for Teacher, Student, or School Manager roles',
  path: ['schoolId'],
});

export const CreateUserForm = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const methods = useForm<ICreateUserFormInput>({
    resolver: zodResolver(createUserSchema),
    defaultValues: {
      role: EUserRole.TEACHER, // Default role
    },
  });

  const { register, handleSubmit, watch, formState: { errors } } = methods;

  const selectedRole = watch('role');
  const showSchoolIdField = [EUserRole.TEACHER, EUserRole.STUDENT, EUserRole.SCHOOL_MANAGER].includes(selectedRole);

  const onSubmitHandler: SubmitHandler<ICreateUserFormInput> = async (formData) => {
    setIsLoading(true);
    setError(null);
    try {
      const result = await handleCreateUserAction(formData);
      if (result.status === 'success') {
        alert(result.message || 'User created successfully!');
        // Optionally reset the form or redirect
        // methods.reset(); 
      } else {
        const errorMessage = Array.isArray(result.message) 
          ? result.message.map(m => `${m.field}: ${m.constraints}`).join(', ') 
          : result.message;
        setError(errorMessage || 'Failed to create user.');
      }
    } catch (err: any) {
      setError(err.message || 'An unexpected error occurred.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Form {...methods}>
      <form onSubmit={handleSubmit(onSubmitHandler)} className="space-y-4">
        <div>
          <Label htmlFor="name">Name</Label>
        <Input id="name" {...register('name')} />
        {errors.name && <p className="text-red-500 text-sm">{errors.name.message}</p>}
      </div>

      <div>
        <Label htmlFor="email">Email</Label>
        <Input id="email" type="email" {...register('email')} />
        {errors.email && <p className="text-red-500 text-sm">{errors.email.message}</p>}
      </div>

      <div>
        <Label htmlFor="password">Password</Label>
        <Input id="password" type="password" {...register('password')} />
        {errors.password && <p className="text-red-500 text-sm">{errors.password.message}</p>}
      </div>

      <div>
        <Label htmlFor="role">Role</Label>
        <select
          id="role"
          {...register('role')}
          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
        >
          {Object.values(EUserRole).map((role) => (
            <option key={role} value={role}>
              {role.charAt(0).toUpperCase() + role.slice(1).replace('_', ' ')}
            </option>
          ))}
        </select>
        {errors.role && <p className="text-red-500 text-sm">{errors.role.message}</p>}
      </div>

      {showSchoolIdField && (
        <div>
          <Label htmlFor="schoolId">School ID</Label>
          <Input id="schoolId" {...register('schoolId')} />
          {errors.schoolId && <p className="text-red-500 text-sm">{errors.schoolId.message}</p>}
        </div>
      )}

      {error && <p className="text-red-500 text-sm">{error}</p>}

      <Button type="submit" disabled={isLoading}>
        {isLoading ? 'Creating...' : 'Create User'}
      </Button>
      </form>
    </Form>
  );
};
