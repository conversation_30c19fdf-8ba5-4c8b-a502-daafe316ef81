'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/atoms/Dialog/Dialog';
import { Button } from '@/components/atoms/Button/Button';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';
import { Document, Page, pdfjs } from 'react-pdf';
import { Loader2, X, Download, Calendar, FileText, User, Info, CheckCircle, AlertTriangle } from 'lucide-react';

// Set the worker source for react-pdf
pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`;

interface ExaminationFormatDialogProps {
  isOpen: boolean;
  onClose: () => void;
  format: any;
}

export const ExaminationFormatDialog: React.FC<ExaminationFormatDialogProps> = ({
  isOpen,
  onClose,
  format,
}) => {
  const [numPages, setNumPages] = useState<number | null>(null);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [validationStatus, setValidationStatus] = useState<'valid' | 'warning' | 'invalid' | 'unknown'>('unknown');

  useEffect(() => {
    // Reset state when dialog opens
    if (isOpen) {
      setPageNumber(1);
      setIsLoading(true);
      setError(null);
      
      // Simulate format validation check
      // In a real app, this would be based on actual validation results
      const randomStatus = Math.random();
      if (randomStatus > 0.7) {
        setValidationStatus('valid');
      } else if (randomStatus > 0.3) {
        setValidationStatus('warning');
      } else {
        setValidationStatus('unknown');
      }
    }
  }, [isOpen]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return bytes + ' bytes';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
  };

  const getDataUrl = () => {
    if (!format?.url) return '';
    return format.url.startsWith('data:') ? format.url : `data:application/pdf;base64,${format.url}`;
  };

  const handleDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    setNumPages(numPages);
    setIsLoading(false);
  };

  const handleDocumentLoadError = (error: Error) => {
    console.error('Error loading PDF:', error);
    setError('Failed to load the PDF document. Please try again later.');
    setIsLoading(false);
  };

  const handlePrevPage = () => {
    setPageNumber(prev => Math.max(prev - 1, 1));
  };

  const handleNextPage = () => {
    if (numPages) {
      setPageNumber(prev => Math.min(prev + 1, numPages));
    }
  };

  const handleDownload = () => {
    if (!format?.url) return;
    
    const link = document.createElement('a');
    link.href = getDataUrl();
    link.download = format.filename || 'examination-format.pdf';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const getValidationIcon = () => {
    switch (validationStatus) {
      case 'valid':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-amber-500" />;
      case 'invalid':
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      default:
        return <Info className="h-5 w-5 text-gray-400" />;
    }
  };

  const getValidationText = () => {
    switch (validationStatus) {
      case 'valid':
        return 'Format validated successfully';
      case 'warning':
        return 'Format may need review';
      case 'invalid':
        return 'Format validation failed';
      default:
        return 'Format not validated';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col overflow-hidden">
        <DialogHeader className="border-b pb-2">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl font-bold">
              Examination Format
            </DialogTitle>
            <button
              onClick={onClose}
              className="rounded-full p-1 hover:bg-gray-100 transition-colors"
              aria-label="Close dialog"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </DialogHeader>

        <div className="flex flex-col md:flex-row gap-6 overflow-auto p-4">
          {/* PDF Viewer */}
          <div className="flex-1 min-w-0 bg-gray-50 rounded-lg border border-gray-200 overflow-hidden">
            {error ? (
              <div className="p-4">
                <AlertMessage type="error" message={error} />
              </div>
            ) : (
              <div className="flex flex-col items-center">
                <div className="w-full max-h-[60vh] overflow-auto p-4">
                  {isLoading && (
                    <div className="flex justify-center items-center h-40">
                      <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
                    </div>
                  )}
                  
                  <Document
                    file={getDataUrl()}
                    onLoadSuccess={handleDocumentLoadSuccess}
                    onLoadError={handleDocumentLoadError}
                    loading={<div className="h-[60vh]"></div>}
                    className="flex justify-center"
                  >
                    <Page 
                      pageNumber={pageNumber} 
                      renderTextLayer={false}
                      renderAnnotationLayer={false}
                      className="shadow-md"
                      scale={1.2}
                    />
                  </Document>
                </div>
                
                {numPages && numPages > 0 && (
                  <div className="flex items-center justify-between w-full px-4 py-2 border-t bg-white">
                    <Button 
                      variant="outline" 
                      onClick={handlePrevPage} 
                      disabled={pageNumber <= 1}
                      className="!w-auto text-sm"
                    >
                      Previous
                    </Button>
                    
                    <p className="text-sm">
                      Page {pageNumber} of {numPages}
                    </p>
                    
                    <Button 
                      variant="outline" 
                      onClick={handleNextPage} 
                      disabled={!numPages || pageNumber >= numPages}
                      className="!w-auto text-sm"
                    >
                      Next
                    </Button>
                  </div>
                )}
              </div>
            )}
          </div>
          
          {/* Format Details */}
          <div className="w-full md:w-72 flex-shrink-0 flex flex-col">
            <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
              <h3 className="font-semibold text-gray-800 mb-3 flex items-center">
                <FileText className="h-4 w-4 mr-2" />
                Format Details
              </h3>
              
              <div className="space-y-3 text-sm">
                <div className="flex items-start">
                  <Calendar className="h-4 w-4 text-gray-500 mt-0.5 mr-2" />
                  <div>
                    <p className="text-gray-500">Uploaded on</p>
                    <p className="font-medium">{format?.createdAt ? formatDate(format.createdAt) : 'N/A'}</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <FileText className="h-4 w-4 text-gray-500 mt-0.5 mr-2" />
                  <div>
                    <p className="text-gray-500">File size</p>
                    <p className="font-medium">{format?.size ? formatFileSize(format.size) : 'N/A'}</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <User className="h-4 w-4 text-gray-500 mt-0.5 mr-2" />
                  <div>
                    <p className="text-gray-500">Uploaded by</p>
                    <p className="font-medium">{format?.uploadedBy || 'N/A'}</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  {getValidationIcon()}
                  <div className="ml-2">
                    <p className="text-gray-500">Validation status</p>
                    <p className="font-medium">{getValidationText()}</p>
                  </div>
                </div>
              </div>
            </div>
            
            <Button 
              variant="primary" 
              onClick={handleDownload} 
              disabled={!format?.url}
              className="mb-2 w-full"
            >
              <Download className="mr-2 h-4 w-4" />
              Download Format
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};