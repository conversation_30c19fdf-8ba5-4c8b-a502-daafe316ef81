'use client';

import React, { useState, useCallback, useRef } from 'react';
import { Button } from '@/components/atoms/Button/Button';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';
import { SchoolSelector } from '@/components/molecules/FormItems/SchoolSelector';
import { uploadExaminationFormatAction } from '@/actions/examinationFormat.action';
import { fetchSchools } from '@/actions/school.action';
import { useForm, Controller } from 'react-hook-form';
import { Upload, File, X, Loader2, CheckCircle, AlertTriangle } from 'lucide-react';
import { ProgressBar } from '@/components/molecules/ProgressBar/ProgressBar';

interface ExaminationFormatUploaderProps {
  schoolId?: string;
  onUploadSuccess?: (response: any) => void;
  onUploadError?: (error: string) => void;
  className?: string;
  isReplacement?: boolean;
}

interface UploadFormData {
  schoolId: string;
}

export const ExaminationFormatUploader: React.FC<ExaminationFormatUploaderProps> = ({
  schoolId: initialSchoolId,
  onUploadSuccess,
  onUploadError,
  className,
  isReplacement = false,
}) => {
  const [file, setFile] = useState<File | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [validationMessage, setValidationMessage] = useState<{type: 'success' | 'error' | 'warning' | null, message: string | null}>({type: null, message: null});
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const { control, handleSubmit, formState: { errors }, watch, setValue } = useForm<UploadFormData>({
    defaultValues: {
      schoolId: initialSchoolId || '',
    },
  });

  const watchedSchoolId = watch('schoolId');

  // Reset file when school changes
  React.useEffect(() => {
    if (initialSchoolId && initialSchoolId !== watchedSchoolId) {
      setValue('schoolId', initialSchoolId);
    }
  }, [initialSchoolId, setValue, watchedSchoolId]);

  const validateFile = (file: File): {isValid: boolean, message: string | null, type: 'success' | 'error' | 'warning' | null} => {
    // Check file type
    if (file.type !== 'application/pdf') {
      return {
        isValid: false, 
        message: 'Only PDF files are allowed', 
        type: 'error'
      };
    }
    
    // Check file size (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    if (file.size > maxSize) {
      return {
        isValid: false, 
        message: 'File size exceeds 5MB limit', 
        type: 'error'
      };
    }

    // Check file size warning (large but acceptable)
    const warningSize = 3 * 1024 * 1024; // 3MB warning
    if (file.size > warningSize) {
      return {
        isValid: true, 
        message: 'File is large (>3MB). Consider optimizing for faster uploads.', 
        type: 'warning'
      };
    }
    
    return {
      isValid: true, 
      message: 'File is valid and ready to upload', 
      type: 'success'
    };
  };

  const handleFileChange = (selectedFile: File | null) => {
    setError(null);
    setSuccess(null);
    setValidationMessage({type: null, message: null});
    
    if (!selectedFile) {
      setFile(null);
      return;
    }
    
    const validation = validateFile(selectedFile);
    if (!validation.isValid) {
      setError(validation.message);
      setFile(null);
      setValidationMessage({type: validation.type, message: validation.message});
      return;
    }
    
    setFile(selectedFile);
    setValidationMessage({type: validation.type, message: validation.message});
  };

  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFileChange(e.dataTransfer.files[0]);
    }
  }, []);

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      handleFileChange(e.target.files[0]);
    }
  };

  const handleRemoveFile = () => {
    setFile(null);
    setError(null);
    setSuccess(null);
    setUploadProgress(0);
    setValidationMessage({type: null, message: null});
    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const simulateProgress = () => {
    setUploadProgress(0);
    const interval = setInterval(() => {
      setUploadProgress((prevProgress) => {
        const newProgress = prevProgress + Math.random() * 10;
        return newProgress >= 90 ? 90 : newProgress; // Cap at 90% until actual completion
      });
    }, 300);
    return interval;
  };

  const onSubmit = async (data: UploadFormData) => {
    if (!file) {
      setError('Please select a file to upload');
      return;
    }

    if (!data.schoolId) {
      setError('Please select a school');
      return;
    }

    setError(null);
    setSuccess(null);
    setIsUploading(true);
    
    // Start progress simulation
    const progressInterval = simulateProgress();

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('schoolId', data.schoolId);

      const response = await uploadExaminationFormatAction(formData);
      
      // Clear progress interval
      clearInterval(progressInterval);
      
      if (response.status === 'success') {
        setUploadProgress(100);
        setSuccess('Examination format uploaded successfully');
        setFile(null);
        setValidationMessage({type: null, message: null});
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        if (onUploadSuccess) {
          onUploadSuccess(response.data);
        }
      } else {
        const errorMsg = typeof response.message === 'string' ? response.message : 'Upload failed';
        setError(errorMsg);
        if (onUploadError) {
          onUploadError(errorMsg);
        }
      }
    } catch (err) {
      clearInterval(progressInterval);
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);
      if (onUploadError) {
        onUploadError(errorMessage);
      }
    } finally {
      setIsUploading(false);
    }
  };

  const getValidationIcon = () => {
    switch (validationMessage.type) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-amber-500" />;
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  return (
    <div className={`${className || ''}`}>
      {!isReplacement && <h3 className="text-lg font-semibold mb-4">Upload Examination Format</h3>}

      {/* Error/Success Messages */}
      {error && <AlertMessage type="error" message={error} />}
      {success && <AlertMessage type="success" message={success} />}

      {/* Upload Form */}
      <form onSubmit={handleSubmit(onSubmit)}>
        {/* School Selector - Only show if schoolId is not provided */}
        {!initialSchoolId && (
          <div className="mb-4">
            <Controller
              name="schoolId"
              control={control}
              rules={{ required: 'Please select a school' }}
              render={({ field }) => (
                <SchoolSelector
                  {...field}
                  label="School"
                  error={errors.schoolId?.message}
                  disabled={isUploading}
                />
              )}
            />
          </div>
        )}

        {/* File Upload Area */}
        <div 
          className={`border-2 border-dashed rounded-lg p-6 transition-all duration-200 ${isDragging ? 'border-blue-400 bg-blue-50' : file ? 'border-green-300 bg-green-50' : 'border-gray-300 bg-gray-50'}`}
          onDragEnter={handleDragEnter}
          onDragLeave={handleDragLeave}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
        >
          {!file ? (
            <div className="flex flex-col items-center justify-center py-4">
              <div className="bg-gray-100 rounded-full p-3 mb-3">
                <Upload className="h-6 w-6 text-gray-500" />
              </div>
              <p className="text-sm font-medium text-gray-700 mb-1">Drag and drop your file here</p>
              <p className="text-xs text-gray-500 mb-3">or click to browse (PDF only, max 5MB)</p>
              
              <input
                type="file"
                id="file-upload"
                className="hidden"
                accept="application/pdf"
                onChange={handleFileInputChange}
                ref={fileInputRef}
                disabled={isUploading}
              />
              
              <Button
                type="button"
                variant="outline"
                onClick={() => fileInputRef.current?.click()}
                disabled={isUploading}
                className="!w-auto"
              >
                <File className="mr-2" />
                Browse Files
              </Button>
            </div>
          ) : (
            <div className="flex flex-col">
              {/* File Preview */}
              <div className="flex items-center justify-between bg-white p-3 rounded-lg border border-gray-200 mb-3">
                <div className="flex items-center">
                  <div className="bg-blue-100 p-2 rounded-full mr-3">
                    <File className="h-5 w-5 text-blue-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">{file.name}</p>
                    <p className="text-xs text-gray-500">{(file.size / 1024).toFixed(1)} KB</p>
                  </div>
                </div>
                
                <button
                  type="button"
                  onClick={handleRemoveFile}
                  className="text-gray-400 hover:text-gray-600"
                  disabled={isUploading}
                  aria-label="Remove file"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>

              {/* Validation Message */}
              {validationMessage.message && (
                <div className={`flex items-center mb-3 text-xs ${validationMessage.type === 'success' ? 'text-green-600' : validationMessage.type === 'warning' ? 'text-amber-600' : 'text-red-600'}`}>
                  {getValidationIcon()}
                  <span className="ml-1">{validationMessage.message}</span>
                </div>
              )}
              
              {/* Upload Progress */}
              {isUploading && (
                <div className="mb-3">
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-xs font-medium text-gray-700">Uploading...</span>
                    <span className="text-xs font-medium text-gray-700">{Math.round(uploadProgress)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                      style={{ width: `${uploadProgress}%` }}
                    ></div>
                  </div>
                </div>
              )}

              {/* Upload Button */}
              <div className="flex justify-end">
                <Button
                  type="submit"
                  variant="primary"
                  disabled={isUploading}
                  className="!w-auto"
                >
                  {isUploading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Uploading...
                    </>
                  ) : (
                    <>
                      <Upload className="mr-2" />
                      {isReplacement ? 'Replace Format' : 'Upload Format'}
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Format Requirements */}
        <div className="mt-3 text-xs text-gray-500">
          <p className="font-medium mb-1">Format Requirements:</p>
          <ul className="list-disc list-inside space-y-1 pl-2">
            <li>PDF format only</li>
            <li>Maximum file size: 5MB</li>
            <li>Clear, readable content</li>
            <li>Standard examination layout recommended</li>
          </ul>
        </div>
      </form>
    </div>
  );
};