'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import {
  School, Mail, Phone, MapPin, Hash, Edit, Building, UserCircle, Briefcase, Tag,
  Users, GraduationCap, ArrowLeft, ExternalLink, Calendar, Clock, Info, BookOpen,
  Award, Home, Palette, Trash2, Alert<PERSON>riangle,
} from 'lucide-react';
import { InfoField } from '@/components/molecules/InfoField/InfoField';
import { ISchoolResponse } from '@/apis/schoolApi';
import { Button } from '@/components/atoms/Button/Button';
import { getFileRenderUrl } from '@/utils/fileUtils';

export interface SchoolDetailCardProps {
  school: ISchoolResponse;
  onEdit?: (school: ISchoolResponse) => void;
  onDelete?: (schoolId: string) => Promise<void>;
  canDelete?: boolean;
}

export const SchoolDetailCard: React.FC<SchoolDetailCardProps> = ({ 
  school, 
  onEdit, 
  onDelete, 
  canDelete = false 
}) => {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleEditClick = () => {
    if (onEdit) {
      onEdit(school);
    }
  };

  const handleDeleteClick = () => {
    setShowDeleteConfirm(true);
  };

  const handleConfirmDelete = async () => {
    if (onDelete) {
      try {
        setIsDeleting(true);
        await onDelete(school.id);
        // The parent component should handle navigation after successful deletion
      } catch (error) {
        console.error('Error deleting school:', error);
        setIsDeleting(false);
        setShowDeleteConfirm(false);
      }
    }
  };

  const handleCancelDelete = () => {
    setShowDeleteConfirm(false);
  };

  // Determine brand color for styling elements
  const brandColor = school.brand?.color || '#3B82F6';
  const avatarGradient = `from-[${brandColor}] to-[${brandColor}]/80`;

  // Format phone number for better readability
  const formatPhoneNumber = (phone: string) => {
    // Simple formatting - could be enhanced based on specific requirements
    return phone.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden transition-all duration-300 relative">
      {/* Delete confirmation dialog */}
      {showDeleteConfirm && (
        <div className="absolute inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6 animate-fade-in">
            <div className="flex items-center text-red-600 mb-4">
              <AlertTriangle size={24} className="mr-2" />
              <h3 className="text-lg font-semibold">Delete School</h3>
            </div>
            <p className="text-gray-700 mb-2">
              Are you sure you want to delete <strong>{school.name}</strong>?
            </p>
            <p className="text-gray-500 text-sm mb-6">
              This action cannot be undone. All data associated with this school will be permanently removed.
            </p>
            <div className="flex justify-end gap-3">
              <Button
                variant="outline"
                onClick={handleCancelDelete}
                disabled={isDeleting}
                className="text-sm"
              >
                Cancel
              </Button>
              <Button
                variant="error"
                onClick={handleConfirmDelete}
                disabled={isDeleting}
                className="bg-red-600 hover:bg-red-700 text-white text-sm flex items-center gap-1.5"
              >
                {isDeleting ? (
                  <>
                    <span className="animate-pulse">Deleting...</span>
                  </>
                ) : (
                  <>
                    <Trash2 size={16} />
                    Delete School
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      )}

      <div className="p-4">
        {/* School header with name and actions */}
        <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-3 mb-4">
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-gray-800">{school.name}</h1>
            {school.registeredNumber && (
              <div className="flex items-center mt-1 text-sm text-gray-500">
                <Tag size={14} className="mr-1" />
                <span>Registered No: {school.registeredNumber}</span>
              </div>
            )}
          </div>

          {/* Action buttons */}
          <div className="flex flex-wrap gap-2">
            {onEdit ? (
              <Button
                variant="outline"
                onClick={handleEditClick}
                className="inline-flex items-center gap-1.5 text-sm"
              >
                <Edit size={16} />
                Edit School
              </Button>
            ) : (
              <Link
                href={`/school-management/edit/${school.id}`}
                className="inline-flex items-center gap-1.5 px-4 py-2 rounded-lg transition-all duration-300 shadow-sm hover:shadow-md font-medium text-sm btn btn-outline"
              >
                <Edit size={16} />
                Edit School
              </Link>
            )}

            {canDelete && onDelete && (
              <Button
                variant="error"
                onClick={handleDeleteClick}
                className="inline-flex items-center gap-1.5 text-sm bg-red-600 hover:bg-red-700 text-white"
                disabled={isDeleting || showDeleteConfirm}
              >
                <Trash2 size={16} />
                Delete School
              </Button>
            )}
          </div>
        </div>

        {/* Essential Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Contact Information */}
          <div>
            {school.address && (
              <div className="group flex items-center gap-2 mb-2">
                <MapPin size={16} className="text-blue-500 flex-shrink-0" />
                <a 
                  href={`https://maps.google.com/?q=${encodeURIComponent(school.address)}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-sm text-gray-700 hover:text-blue-600"
                >
                  {school.address}
                </a>
              </div>
            )}

            {school.email && (
              <div className="group flex items-center gap-2 mb-2">
                <Mail size={16} className="text-green-500 flex-shrink-0" />
                <a 
                  href={`mailto:${school.email}`}
                  className="text-sm text-gray-700 hover:text-green-600"
                >
                  {school.email}
                </a>
              </div>
            )}

            {school.phoneNumber && (
              <div className="group flex items-center gap-2 mb-2">
                <Phone size={16} className="text-purple-500 flex-shrink-0" />
                <a 
                  href={`tel:${school.phoneNumber}`}
                  className="text-sm text-gray-700 hover:text-purple-600"
                >
                  {formatPhoneNumber(school.phoneNumber)}
                </a>
              </div>
            )}
          </div>

          {/* School Details */}
          <div>
            {school.admin && (
              <div className="group flex items-center gap-2 mb-2">
                <UserCircle size={16} className="text-indigo-500 flex-shrink-0" />
                <div className="text-sm text-gray-700">
                  <span>{school.admin.name}</span>
                  {school.admin.email && (
                    <a 
                      href={`mailto:${school.admin.email}`}
                      className="block text-xs text-gray-500 hover:text-indigo-600"
                    >
                      {school.admin.email}
                    </a>
                  )}
                </div>
              </div>
            )}

            <div className="flex items-center gap-2 mb-2">
              <Hash size={16} className="text-gray-500 flex-shrink-0" />
              <span className="text-sm text-gray-700">{school.id}</span>
            </div>

            {school.brand && (
              <div className="flex items-start gap-2">
                <Palette size={16} className="text-purple-500 flex-shrink-0 mt-0.5" />
                <div className="flex flex-col">
                  <span className="text-sm text-gray-700 font-medium mb-2">Brand Details</span>

                  {school.brand.color && (
                    <div className="flex items-center mb-2">
                      <div
                        className="w-4 h-4 rounded-full mr-2"
                        style={{ backgroundColor: school.brand.color }}
                      ></div>
                      <span className="text-sm text-gray-700">Color: {school.brand.color}</span>
                    </div>
                  )}

                  {school.brand.logo && (
                    <div className="mb-2">
                      <span className="text-xs text-gray-500 block mb-1">Logo:</span>
                      <div className="relative w-16 h-16 border border-gray-200 rounded-md bg-white overflow-hidden flex items-center justify-center p-1">
                        <Image
                          src={getFileRenderUrl(school.brand.logo)}
                          alt="School Logo"
                          width={56}
                          height={56}
                          className="object-contain max-w-full max-h-full"
                          onError={() => {
                            // Handle error if needed
                          }}
                        />
                      </div>
                    </div>
                  )}

                  {school.brand.image && (
                    <div className="mb-2">
                      <span className="text-xs text-gray-500 block mb-1">Brand Image:</span>
                      <div className="relative w-20 h-12 border border-gray-200 rounded-md overflow-hidden flex items-center justify-center p-1">
                        <Image
                          src={getFileRenderUrl(school.brand.image)}
                          alt="Brand Image"
                          width={72}
                          height={40}
                          className="object-cover max-w-full max-h-full"
                          onError={() => {
                            // Handle error if needed
                          }}
                        />
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Record information footer */}
        <div className="mt-4 pt-3 border-t border-gray-100 text-xs text-gray-500 flex flex-wrap gap-x-4 gap-y-1">
          {school.createdAt && (
            <div className="flex items-center">
              <Calendar size={12} className="mr-1" />
              <span>Created: {new Date(school.createdAt).toLocaleDateString()}</span>
            </div>
          )}
          {school.updatedAt && school.updatedAt !== school.createdAt && (
            <div className="flex items-center">
              <Clock size={12} className="mr-1" />
              <span>Updated: {new Date(school.updatedAt).toLocaleDateString()}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
