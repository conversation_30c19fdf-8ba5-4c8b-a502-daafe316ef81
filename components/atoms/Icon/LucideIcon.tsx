import React from 'react';
import {
  ChevronDown,
  Eye,
  EyeOff,
  Lightbulb,
  CheckCircle,
  XCircle,
  RefreshCcw,
  AlertTriangle,
  Edit,
  Pen,
  FileText,
  Target,
  BookOpen,
  GraduationCap,
  TrendingUp,
  Globe,
  Hash,
  List,
  Trash,
  Check,
  ArrowUp,
  ArrowDown,
  Star,
  ClipboardList,
  HelpCircle,
  X,
  Trash2,
  Plus,
  ArrowLeft, // Added ArrowLeft
  Upload,
} from 'lucide-react';

import { cn } from '@/utils/cn';

export type LucideIconVariant =
  | 'chevron-down'
  | 'eye'
  | 'eye-slash'
  | 'lightbulb'
  | 'check-circle'
  | 'x-circle'
  | 'refresh-ccw'
  | 'refresh'
  | 'alert-triangle'
  | 'edit'
  | 'pen'
  | 'file-text'
  | 'target'
  | 'book-open'
  | 'graduation-cap'
  | 'trending-up'
  | 'globe'
  | 'hash'
  | 'list'
  | 'trash'
  | 'check'
  | 'arrow-up'
  | 'arrow-down'
  | 'star'
  | 'clipboard-list'
  | 'help-circle'
  | 'x'
  | 'trash-2'
  | 'plus'
  | 'arrow-left' // Added arrow-left
  | 'upload';

interface LucideIconProps {
  variant: LucideIconVariant;
  size?: number;
  className?: string;
  strokeWidth?: number;
}

const iconMap: Record<LucideIconVariant, React.ComponentType<any>> = {
  'chevron-down': ChevronDown,
  'eye': Eye,
  'eye-slash': EyeOff,
  'lightbulb': Lightbulb,
  'check-circle': CheckCircle,
  'x-circle': XCircle,
  'refresh-ccw': RefreshCcw,
  'refresh': RefreshCcw, // Using RefreshCcw for refresh variant
  'alert-triangle': AlertTriangle,
  'edit': Edit,
  'pen': Pen,
  'file-text': FileText,
  'target': Target,
  'book-open': BookOpen,
  'graduation-cap': GraduationCap,
  'trending-up': TrendingUp,
  'globe': Globe,
  'hash': Hash,
  'list': List,
  'trash': Trash,
  'check': Check,
  'arrow-up': ArrowUp,
  'arrow-down': ArrowDown,
  'star': Star,
  'clipboard-list': ClipboardList,
  'help-circle': HelpCircle,
  'x': X,
  'trash-2': Trash2,
  'plus': Plus,
  'arrow-left': ArrowLeft, // Added arrow-left mapping
  'upload': Upload,
};

export const LucideIcon: React.FC<LucideIconProps> = ({
  variant,
  size = 16,
  className = '',
  strokeWidth = 2,
}) => {
  const IconComponent = iconMap[variant];

  if (!IconComponent) {
    console.warn(`LucideIcon: Icon variant "${variant}" not found`);
    return null;
  }

  return (
    <IconComponent
      size={size}
      strokeWidth={strokeWidth}
      className={cn('inline-block', className)}
    />
  );
};
