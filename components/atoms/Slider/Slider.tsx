import React from 'react';
import { cn } from '@/utils/cn';

// Add global styles for the slider thumb
const sliderStyles = `
  /* Common styles for the range input */
  input[type="range"] {
    -webkit-appearance: none;
    appearance: none;
    touch-action: none; /* Improve touch handling */
    height: 4px; /* Slightly taller track */
  }

  /* Webkit (Chrome, Safari, Edge) */
  input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: var(--thumb-size, 24px);
    height: var(--thumb-size, 24px);
    background: var(--thumb-color, white);
    border: var(--thumb-border, 2px solid #3872FA);
    border-radius: 50%;
    cursor: grab;
    margin-top: -10px; /* Offset to center the thumb on the track */
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 20;
    position: relative;
    transition: transform 0.1s ease, box-shadow 0.1s ease;
  }

  /* Active state for better feedback */
  input[type="range"]::-webkit-slider-thumb:active {
    cursor: grabbing;
    transform: scale(1.1);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
  }

  /* Hover state for better feedback */
  input[type="range"]::-webkit-slider-thumb:hover {
    transform: scale(1.05);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.25);
  }

  /* Firefox */
  input[type="range"]::-moz-range-thumb {
    width: var(--thumb-size, 24px);
    height: var(--thumb-size, 24px);
    background: var(--thumb-color, white);
    border: var(--thumb-border, 2px solid #3872FA);
    border-radius: 50%;
    cursor: grab;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 20;
    position: relative;
    transition: transform 0.1s ease, box-shadow 0.1s ease;
  }

  /* Active state for Firefox */
  input[type="range"]::-moz-range-thumb:active {
    cursor: grabbing;
    transform: scale(1.1);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
  }

  /* Hover state for Firefox */
  input[type="range"]::-moz-range-thumb:hover {
    transform: scale(1.05);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.25);
  }
`;

export interface SliderProps extends React.InputHTMLAttributes<HTMLInputElement> {
  min?: number;
  max?: number;
  step?: number;
  value?: number;
  defaultValue?: number;
  onChange?: (value: number) => void;
  className?: string;
  thumbClassName?: string;
  trackClassName?: string;
  progressClassName?: string;
  showValue?: boolean;
  valuePrefix?: string;
  valueSuffix?: string;
}

export const Slider: React.FC<SliderProps> = ({
  min = 0,
  max = 100,
  step = 1,
  value,
  defaultValue,
  onChange,
  className,
  thumbClassName,
  trackClassName,
  progressClassName,
  showValue = false,
  valuePrefix = '',
  valueSuffix = '',
  ...props
}) => {
  const [internalValue, setInternalValue] = React.useState<number>(
    value !== undefined ? value : defaultValue !== undefined ? defaultValue : min
  );

  React.useEffect(() => {
    if (value !== undefined) {
      setInternalValue(value);
    }
  }, [value]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = Number(e.target.value);
    setInternalValue(newValue);
    onChange?.(newValue);
  };

  // Calculate the progress percentage for styling
  const progressPercentage = ((internalValue - min) / (max - min)) * 100;

  return (
    <div className={cn("relative w-full", className)}>
      {/* Add the slider styles */}
      <style dangerouslySetInnerHTML={{ __html: sliderStyles }} />
      <div className="relative">
        <input
          type="range"
          min={min}
          max={max}
          step={step}
          value={internalValue}
          onChange={handleChange}
          className={cn(
            "w-full h-4 appearance-none bg-transparent cursor-pointer z-10 relative",
            "focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-opacity-50 rounded-md",
            "hover:cursor-grab active:cursor-grabbing",
            props.disabled && "cursor-not-allowed opacity-50",
            thumbClassName
          )}
          style={{
            // Custom styling for webkit browsers
            WebkitAppearance: 'none',
            // Add thumb styling
            '--thumb-color': 'white',
            '--thumb-border': '2px solid #3872FA',
            '--thumb-size': '24px',
            touchAction: 'none', // Improve touch handling
          }}
          // Add CSS custom properties for the thumb
          data-thumb-color={props.style?.['--thumb-color'] || 'white'}
          data-thumb-border={props.style?.['--thumb-border'] || '2px solid #3872FA'}
          data-thumb-size={props.style?.['--thumb-size'] || '24px'}
          {...props}
        />
        {/* Track background */}
        <div
          className={cn(
            "absolute top-1/2 left-0 h-3 -translate-y-1/2 rounded-full bg-gray-200 z-0",
            trackClassName
          )}
          style={{ width: '100%' }}
        />
        {/* Progress fill */}
        <div
          className={cn(
            "absolute top-1/2 left-0 h-3 -translate-y-1/2 rounded-full bg-primary-500 z-0",
            progressClassName
          )}
          style={{ width: `${progressPercentage}%` }}
        />
      </div>

      {showValue && (
        <div className="mt-2 text-sm font-medium text-gray-700">
          {valuePrefix}{internalValue}{valueSuffix}
        </div>
      )}
    </div>
  );
};

export default Slider;
